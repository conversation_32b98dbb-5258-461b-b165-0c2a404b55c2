<script setup lang="ts">
import { ref } from 'vue';
import { useAuthStore } from '@/stores/auth.ts';
import { useTranslation } from 'i18next-vue';
import EmployeesList from './EmployeesList.vue';
import EmployeeCreate from './EmployeeCreate.vue';

const { t } = useTranslation();
const authStore = useAuthStore();
const showCreateForm = ref(false);
const employeesListRef = ref();

function openCreateForm() {
    showCreateForm.value = true;
}

function closeCreateForm() {
    showCreateForm.value = false;
}

function onEmployeeCreated() {
    showCreateForm.value = false;
    // Refresh the employees list
    if (employeesListRef.value) {
        employeesListRef.value.refreshList();
    }
}
</script>
<template>
    <div>
        <div class="card">
            <Toolbar class="mb-6">
                <template #end>
                    <Button :label="t('createEmployee')" icon="pi pi-plus" severity="primary" class="mr-2" v-if="authStore.hasPrivilege('EMPLOYEE_CREATE')" @click="openCreateForm" />
                </template>
            </Toolbar>

            <Dialog v-model:visible="showCreateForm" :header="t('createEmployee')" :modal="true" :style="{ width: '50rem' }" @hide="closeCreateForm">
                <EmployeeCreate @employee-created="onEmployeeCreated" @cancel="closeCreateForm" />
            </Dialog>

            <EmployeesList ref="employeesListRef" />
        </div>
    </div>
</template>
