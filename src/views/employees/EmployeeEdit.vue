<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useEmployeesStore } from '@/stores/employees';
import { Employee, UpdateEmployeeInput } from '@/services/employees/types';
import { ApiResponse } from '@/services/types';
import { useToast } from 'primevue/usetoast';
import { useTranslation } from 'i18next-vue';
import { mapErrorCode } from '@/utilities/ErrorCodeMapper';

const { t } = useTranslation();

// Props
const props = defineProps<{
    employee: Employee;
}>();

// Stores
const employeesStore = useEmployeesStore();
const toast = useToast();

// Emits
const emit = defineEmits<{
    employeeUpdated: [];
    cancel: [];
}>();

// Refs
const loading = ref(false);
const backendError = ref('');

// Form data
const formData = ref({
    firstName: '',
    lastName: '',
    phoneNumber: '',
    email: ''
});

// Initialize form data
onMounted(() => {
    formData.value = {
        firstName: props.employee.firstName,
        lastName: props.employee.lastName,
        phoneNumber: props.employee.phoneNumber,
        email: props.employee.email || ''
    };
});

// Form validation
function validateForm(): boolean {
    if (!formData.value.firstName.trim()) {
        backendError.value = t('firstNameRequired');
        return false;
    }
    if (!formData.value.lastName.trim()) {
        backendError.value = t('lastNameRequired');
        return false;
    }
    if (!formData.value.phoneNumber.trim()) {
        backendError.value = t('phoneNumberRequired');
        return false;
    }
    
    return true;
}

// Handle form submission
async function handleSubmit() {
    backendError.value = '';

    if (!validateForm()) {
        return;
    }

    loading.value = true;

    const updateEmployeeInput: UpdateEmployeeInput = {
        id: props.employee.id,
        firstName: formData.value.firstName,
        lastName: formData.value.lastName,
        phoneNumber: formData.value.phoneNumber,
        email: formData.value.email || undefined
    };

    const response: ApiResponse<Employee> = await employeesStore.dispatchUpdateEmployee(updateEmployeeInput);

    loading.value = false;

    if (response.success) {
        emit('employeeUpdated');
    } else {
        backendError.value = mapErrorCode(response.errorResponse?.codeName);
    }
}

function handleCancel() {
    emit('cancel');
}
</script>

<template>
    <div>
        <form @submit.prevent="handleSubmit">
            <div class="grid">
                <div class="col-12">
                    <div class="field">
                        <label for="firstName">{{ t('firstName') }} *</label>
                        <InputText
                            id="firstName"
                            v-model="formData.firstName"
                            :class="{ 'p-invalid': backendError && !formData.firstName }"
                            class="w-full"
                            required
                        />
                    </div>
                </div>

                <div class="col-12">
                    <div class="field">
                        <label for="lastName">{{ t('lastName') }} *</label>
                        <InputText
                            id="lastName"
                            v-model="formData.lastName"
                            :class="{ 'p-invalid': backendError && !formData.lastName }"
                            class="w-full"
                            required
                        />
                    </div>
                </div>

                <div class="col-12">
                    <div class="field">
                        <label for="phoneNumber">{{ t('phoneNumber') }} *</label>
                        <InputText
                            id="phoneNumber"
                            v-model="formData.phoneNumber"
                            :class="{ 'p-invalid': backendError && !formData.phoneNumber }"
                            class="w-full"
                            required
                        />
                    </div>
                </div>

                <div class="col-12">
                    <div class="field">
                        <label for="email">{{ t('email') }}</label>
                        <InputText
                            id="email"
                            v-model="formData.email"
                            type="email"
                            class="w-full"
                        />
                        <small class="text-surface-500">{{ t('optional') }}</small>
                    </div>
                </div>

                <!-- Show system access info if employee has user account -->
                <div v-if="employee.user" class="col-12">
                    <div class="p-3 surface-100 border-round">
                        <div class="flex align-items-center gap-2 mb-2">
                            <i class="pi pi-user text-primary"></i>
                            <span class="font-semibold">{{ t('systemAccess') }}</span>
                        </div>
                        <p class="text-sm text-surface-600 m-0">
                            {{ t('hasSystemAccess') }}: {{ employee.user.username }}
                        </p>
                        <p class="text-sm text-surface-500 m-0 mt-1">
                            {{ t('role') }}: {{ t(employee.user.role) }}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Error Message -->
            <div v-if="backendError" class="p-error mb-3">
                {{ backendError }}
            </div>

            <!-- Form Actions -->
            <div class="flex justify-content-end gap-2">
                <Button :label="t('cancel')" icon="pi pi-times" @click="handleCancel" class="p-button-text" />
                <Button :label="t('saveChanges')" icon="pi pi-check" type="submit" :loading="loading" />
            </div>
        </form>
    </div>
</template>
