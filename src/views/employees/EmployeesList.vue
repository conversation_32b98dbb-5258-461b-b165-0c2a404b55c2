<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useEmployeesStore } from '@/stores/employees';
import { useAuthStore } from '@/stores/auth';
import { storeToRefs } from 'pinia';
import { GetEmployeesInput, Employee } from '@/services/employees/types';
import { formatDate } from '@/utilities/DateUtils';

import { useToast } from 'primevue/usetoast';
import { useTranslation } from 'i18next-vue';
import EmployeeEdit from './EmployeeEdit.vue';

const { t } = useTranslation();

// Stores
const employeesStore = useEmployeesStore();
const authStore = useAuthStore();
const { employees, totalElements } = storeToRefs(employeesStore);
const toast = useToast();

// Refs
const dt = ref();
const loading = ref(false);
const selectedEmployee = ref<Employee | null>(null);
const employeeToEdit = ref<Employee | null>(null);
const employeeDetailsVisible = ref(false);
const employeeEditVisible = ref(false);

// Pagination and filtering
const first = ref(0);
const rows = ref(10);
const sortField = ref<string>('createDate');
const sortOrder = ref<number>(-1);
const firstnameFilter = ref<string>('');

// Computed properties
const currentPage = computed(() => Math.floor(first.value / rows.value) + 1);

// Load employees on component mount
onMounted(async () => {
    await loadEmployees();
});

// Load employees function
async function loadEmployees() {
    loading.value = true;

    const input: GetEmployeesInput = {
        pageSize: rows.value,
        pageNumber: currentPage.value,
        firstnameFilter: firstnameFilter.value || undefined,
        sortBy: sortField.value,
        sortDirection: sortOrder.value === 1 ? 'ASC' : sortOrder.value === -1 ? 'DESC' : undefined
    };

    await employeesStore.dispatchGetEmployees(input);
    loading.value = false;
}

// Event handlers
function onPage(event: any) {
    first.value = event.first;
    rows.value = event.rows;
    loadEmployees();
}

function onSort(event: any) {
    sortField.value = event.sortField;
    sortOrder.value = event.sortOrder;
    loadEmployees();
}

function onFilter() {
    first.value = 0; // Reset to first page when filtering
    loadEmployees();
}

// Expose refresh method for parent component
function refreshList() {
    loadEmployees();
}

// Expose the refresh method
defineExpose({
    refreshList
});

function editEmployee(employee: Employee) {
    employeeToEdit.value = { ...employee }; // Create a copy to avoid mutating the original
    employeeEditVisible.value = true;
    // Close the employee details dialog when opening edit modal
    employeeDetailsVisible.value = false;
    selectedEmployee.value = null;
}

async function onRowClick(event: any) {
    selectedEmployee.value = event.data;
    employeeDetailsVisible.value = true;
}

function hideEmployeeDetails() {
    employeeDetailsVisible.value = false;
    selectedEmployee.value = null;
}

function hideEmployeeEdit() {
    employeeEditVisible.value = false;
    employeeToEdit.value = null;
}

function onEmployeeUpdated() {
    hideEmployeeEdit();
    loadEmployees(); // Refresh the list
    toast.add({
        severity: 'success',
        summary: t('success'),
        detail: t('employeeUpdatedSuccessfully'),
        life: 3000
    });
}

// Delete employee function
async function deleteEmployee(employee: Employee) {
    const response = await employeesStore.dispatchDeleteEmployee(employee.id);

    if (response.success) {
        toast.add({
            severity: 'success',
            summary: t('success'),
            detail: t('employeeDeletedSuccessfully'),
            life: 3000
        });
        // Refresh the list to reflect the changes
        loadEmployees();
    } else {
        toast.add({
            severity: 'error',
            summary: t('error'),
            detail: t('employeeDeleteError'),
            life: 3000
        });
    }
}
</script>

<template>
    <div>
        <DataTable
            ref="dt"
            :value="employees"
            dataKey="id"
            :paginator="true"
            :rows="rows"
            :first="first"
            :totalRecords="totalElements"
            :loading="loading"
            :lazy="true"
            :sortField="sortField"
            :sortOrder="sortOrder"
            selectionMode="single"
            paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
            :rowsPerPageOptions="[5, 10, 25, 50]"
            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} employees"
            @page="onPage"
            @sort="onSort"
            @row-click="onRowClick"
            stripedRows
        >
            <template #header>
                <div class="flex flex-wrap gap-2 items-center justify-between">
                    <h4 class="m-0">{{ t('manageEmployees') }}</h4>
                    <IconField>
                        <InputIcon>
                            <i class="pi pi-search" />
                        </InputIcon>
                        <InputText v-model="firstnameFilter" :placeholder="t('searchByFirstName')" @input="onFilter" />
                    </IconField>
                </div>
            </template>
            <template #empty> No employees found. </template>
            <template #loading> Loading employees data. Please wait. </template>

            <Column field="firstName" :header="t('firstName')" :sortable="true" style="min-width: 12rem">
                <template #body="{ data }">
                    <div class="flex align-items-center gap-2">
                        <span>{{ data.firstName }} {{ data.lastName }}</span>
                    </div>
                </template>
            </Column>

            <Column field="email" :header="t('email')" :sortable="true" style="min-width: 12rem">
                <template #body="{ data }">
                    <span>{{ data.email || '-' }}</span>
                </template>
            </Column>

            <Column field="phoneNumber" :header="t('phoneNumber')" :sortable="true" style="min-width: 10rem">
                <template #body="{ data }">
                    <span>{{ data.phoneNumber }}</span>
                </template>
            </Column>

            <Column field="user" :header="t('systemAccess')" style="min-width: 8rem">
                <template #body="{ data }">
                    <Tag v-if="data.user" :value="t('EMPLOYEE')" severity="success" />
                    <span v-else class="text-surface-500">-</span>
                </template>
            </Column>

            <Column field="createDate" :header="t('createDate')" :sortable="true" style="min-width: 10rem">
                <template #body="{ data }">
                    <span>{{ formatDate(data.createDate) }}</span>
                </template>
            </Column>

            <Column :exportable="false" style="min-width: 8rem">
                <template #body="slotProps">
                    <Button icon="pi pi-pencil" outlined rounded class="mr-2" v-if="authStore.hasPrivilege('EMPLOYEE_UPDATE')" @click="editEmployee(slotProps.data)" />
                    <Button
                        icon="pi pi-trash"
                        outlined
                        rounded
                        severity="danger"
                        v-if="authStore.hasPrivilege('EMPLOYEE_DELETE')"
                        @click="deleteEmployee(slotProps.data)"
                    />
                </template>
            </Column>
        </DataTable>
