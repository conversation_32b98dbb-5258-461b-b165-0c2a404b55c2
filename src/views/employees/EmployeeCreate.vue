<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useEmployeesStore } from '@/stores/employees';
import { useUsersStore } from '@/stores/users';
import { CreateEmployeeInput, Employee } from '@/services/employees/types';
import { PrivilegeOption } from '@/services/users/types';
import { ApiResponse } from '@/services/types';
import { useToast } from 'primevue/usetoast';
import { useTranslation } from 'i18next-vue';
import { mapErrorCode } from '@/utilities/ErrorCodeMapper';

const { t } = useTranslation();

// Stores
const employeesStore = useEmployeesStore();
const usersStore = useUsersStore();
const toast = useToast();

// Emits
const emit = defineEmits<{
    employeeCreated: [];
    cancel: [];
}>();

// Refs
const loading = ref(false);
const backendError = ref('');
const privilegeOptions = ref<PrivilegeOption[]>([]);

// Form data
const formData = ref({
    firstName: '',
    lastName: '',
    phoneNumber: '',
    systemAccess: false,
    email: '',
    password: '',
    confirmPassword: '',
    privileges: [] as string[]
});

// Computed properties
const privilegesByGroup = computed(() => {
    const groups: { [key: string]: PrivilegeOption[] } = {};
    privilegeOptions.value.forEach((option) => {
        if (!groups[option.group]) {
            groups[option.group] = [];
        }
        groups[option.group].push(option);
    });
    return groups;
});

// Load privilege options on component mount
onMounted(async () => {
    await loadPrivilegeOptions();
});

// Load privilege options
async function loadPrivilegeOptions() {
    const response = await usersStore.dispatchGetPrivilegeOptions();
    if (response.success && response.content) {
        privilegeOptions.value = response.content;
    }
}

// Form validation
function validateForm(): boolean {
    if (!formData.value.firstName.trim()) {
        backendError.value = t('firstNameRequired');
        return false;
    }
    if (!formData.value.lastName.trim()) {
        backendError.value = t('lastNameRequired');
        return false;
    }
    if (!formData.value.phoneNumber.trim()) {
        backendError.value = t('phoneNumberRequired');
        return false;
    }
    
    if (formData.value.systemAccess) {
        if (!formData.value.email.trim()) {
            backendError.value = t('emailRequired');
            return false;
        }
        if (!formData.value.password.trim()) {
            backendError.value = t('passwordRequired');
            return false;
        }
        if (formData.value.password !== formData.value.confirmPassword) {
            backendError.value = t('passwordsDoNotMatch');
            return false;
        }
        if (formData.value.password.length < 5) {
            backendError.value = t('passwordMinLength');
            return false;
        }
        if (formData.value.privileges.length === 0) {
            backendError.value = t('privilegesRequired');
            return false;
        }
    }
    
    return true;
}

// Handle form submission
async function handleSubmit() {
    backendError.value = '';

    if (!validateForm()) {
        return;
    }

    loading.value = true;

    const createEmployeeInput: CreateEmployeeInput = {
        firstName: formData.value.firstName,
        lastName: formData.value.lastName,
        phoneNumber: formData.value.phoneNumber,
        systemAccess: formData.value.systemAccess
    };

    if (formData.value.systemAccess) {
        createEmployeeInput.email = formData.value.email;
        createEmployeeInput.password = formData.value.password;
        createEmployeeInput.privileges = formData.value.privileges;
    }

    const response: ApiResponse<Employee> = await employeesStore.dispatchCreateEmployee(createEmployeeInput);

    loading.value = false;

    if (response.success) {
        toast.add({
            severity: 'success',
            summary: t('success'),
            detail: t('employeeCreatedSuccessfully'),
            life: 3000
        });
        emit('employeeCreated');
    } else {
        backendError.value = mapErrorCode(response.errorResponse?.codeName);
    }
}

function handleCancel() {
    emit('cancel');
}

// Handle system access toggle
function onSystemAccessChange() {
    if (!formData.value.systemAccess) {
        // Clear system access related fields when disabled
        formData.value.email = '';
        formData.value.password = '';
        formData.value.confirmPassword = '';
        formData.value.privileges = [];
    }
}
</script>

<template>
    <div>
        <form @submit.prevent="handleSubmit">
            <div class="grid">
                <!-- Basic Employee Information -->
                <div class="col-12">
                    <div class="field">
                        <label for="firstName">{{ t('firstName') }} *</label>
                        <InputText
                            id="firstName"
                            v-model="formData.firstName"
                            :class="{ 'p-invalid': backendError && !formData.firstName }"
                            class="w-full"
                            required
                        />
                    </div>
                </div>

                <div class="col-12">
                    <div class="field">
                        <label for="lastName">{{ t('lastName') }} *</label>
                        <InputText
                            id="lastName"
                            v-model="formData.lastName"
                            :class="{ 'p-invalid': backendError && !formData.lastName }"
                            class="w-full"
                            required
                        />
                    </div>
                </div>

                <div class="col-12">
                    <div class="field">
                        <label for="phoneNumber">{{ t('phoneNumber') }} *</label>
                        <InputText
                            id="phoneNumber"
                            v-model="formData.phoneNumber"
                            :class="{ 'p-invalid': backendError && !formData.phoneNumber }"
                            class="w-full"
                            required
                        />
                    </div>
                </div>

                <!-- System Access Section -->
                <div class="col-12">
                    <div class="field-checkbox">
                        <Checkbox
                            id="systemAccess"
                            v-model="formData.systemAccess"
                            :binary="true"
                            @change="onSystemAccessChange"
                        />
                        <label for="systemAccess">{{ t('createSystemAccess') }}</label>
                    </div>
                </div>

                <!-- System Access Fields (shown only when systemAccess is true) -->
                <template v-if="formData.systemAccess">
                    <div class="col-12">
                        <div class="field">
                            <label for="email">{{ t('email') }} *</label>
                            <InputText
                                id="email"
                                v-model="formData.email"
                                type="email"
                                :class="{ 'p-invalid': backendError && !formData.email }"
                                class="w-full"
                                required
                            />
                        </div>
                    </div>

                    <div class="col-12">
                        <div class="field">
                            <label for="password">{{ t('password') }} *</label>
                            <Password
                                id="password"
                                v-model="formData.password"
                                :class="{ 'p-invalid': backendError && !formData.password }"
                                class="w-full"
                                toggleMask
                                :feedback="false"
                                required
                            />
                        </div>
                    </div>

                    <div class="col-12">
                        <div class="field">
                            <label for="confirmPassword">{{ t('confirmPassword') }} *</label>
                            <Password
                                id="confirmPassword"
                                v-model="formData.confirmPassword"
                                :class="{ 'p-invalid': backendError && formData.password !== formData.confirmPassword }"
                                class="w-full"
                                toggleMask
                                :feedback="false"
                                required
                            />
                        </div>
                    </div>

                    <!-- Privileges Section -->
                    <div class="col-12">
                        <div class="field">
                            <label>{{ t('privileges') }} *</label>
                            <div class="mt-2">
                                <div v-for="(groupOptions, groupName) in privilegesByGroup" :key="groupName" class="mb-4">
                                    <h6 class="text-primary mb-2">{{ t(groupName) }}</h6>
                                    <div class="grid">
                                        <div v-for="option in groupOptions" :key="option.name" class="col-6">
                                            <div class="field-checkbox">
                                                <Checkbox
                                                    :id="option.name"
                                                    v-model="formData.privileges"
                                                    :value="option.name"
                                                    :disabled="option.disabled"
                                                />
                                                <label :for="option.name" :class="{ 'text-surface-400': option.disabled }">
                                                    {{ t(option.name) }}
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>

            <!-- Error Message -->
            <div v-if="backendError" class="p-error mb-3">
                {{ backendError }}
            </div>

            <!-- Form Actions -->
            <div class="flex justify-content-end gap-2">
                <Button :label="t('cancel')" icon="pi pi-times" @click="handleCancel" class="p-button-text" />
                <Button :label="t('saveChanges')" icon="pi pi-check" type="submit" :loading="loading" />
            </div>
        </form>
    </div>
</template>
