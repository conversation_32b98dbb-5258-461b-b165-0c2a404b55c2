import { defineStore } from 'pinia';
import { ref } from 'vue';
import { CreateEmployeeInput, Employee, GetEmployeesInput, UpdateEmployeeInput } from '../services/employees/types';
import { ApiErrorResponse, ApiResponse, PagedResource } from '../services/types';
import { API } from '../services';
import { AxiosError } from 'axios';

export const useEmployeesStore = defineStore('employeesStore', () => {
    const employees = ref<Employee[]>([]);
    const totalElements = ref<number>(0);
    const totalPages = ref<number>(0);
    const pageSize = ref<number | undefined>(undefined);
    const pageElements = ref<number | undefined>(undefined);
    const pageNumber = ref<number | undefined>(undefined);

    function initEmployees(data: PagedResource<Employee>) {
        employees.value = data.content;
        totalElements.value = data.totalElements;
        totalPages.value = data.totalPages;
        pageSize.value = data.pageSize;
        pageNumber.value = data.pageNumber;
        pageElements.value = data.pageElements;
    }

    function addNewEmployee(employee: Employee) {
        employees.value.push(employee);
    }

    function updateEmployee(employee: Employee) {
        const idx = employees.value.findIndex((e) => e.id === employee.id);
        if (idx === -1) return;
        employees.value.splice(idx, 1, employee);
    }

    function removeEmployee(id: string) {
        const idx = employees.value.findIndex((e) => e.id === id);
        if (idx !== -1) {
            employees.value.splice(idx, 1);
            totalElements.value = Math.max(0, totalElements.value - 1);
        }
    }

    async function dispatchGetEmployees(input: GetEmployeesInput): Promise<ApiResponse<PagedResource<Employee>>> {
        try {
            const { status, data } = await API.employees.getEmployees(input);
            initEmployees(data);
            return {
                success: true,
                content: data,
                status: status
            };
        } catch (error) {
            const _error = error as AxiosError<ApiErrorResponse>;
            return {
                success: false,
                status: _error.response?.status,
                errorResponse: _error.response?.data
            };
        }
    }

    async function dispatchGetEmployee(employeeId: string): Promise<ApiResponse<Employee>> {
        try {
            const { status, data } = await API.employees.getEmployee(employeeId);
            updateEmployee(data);
            return {
                success: true,
                content: data,
                status: status
            };
        } catch (error) {
            const _error = error as AxiosError<ApiErrorResponse>;
            return {
                success: false,
                status: _error.response?.status,
                errorResponse: _error.response?.data
            };
        }
    }

    async function dispatchCreateEmployee(input: CreateEmployeeInput): Promise<ApiResponse<Employee>> {
        try {
            const { status, data } = await API.employees.createEmployee(input);
            addNewEmployee(data);
            return {
                success: true,
                content: data,
                status: status
            };
        } catch (error) {
            const _error = error as AxiosError<ApiErrorResponse>;
            return {
                success: false,
                status: _error.response?.status,
                errorResponse: _error.response?.data
            };
        }
    }

    async function dispatchUpdateEmployee(input: UpdateEmployeeInput): Promise<ApiResponse<Employee>> {
        try {
            const { status, data } = await API.employees.updateEmployee(input);
            updateEmployee(data);
            return {
                success: true,
                content: data,
                status: status
            };
        } catch (error) {
            const _error = error as AxiosError<ApiErrorResponse>;
            return {
                success: false,
                status: _error.response?.status,
                errorResponse: _error.response?.data
            };
        }
    }

    async function dispatchDeleteEmployee(id: string): Promise<ApiResponse<void>> {
        try {
            const { status } = await API.employees.deleteEmployee(id);
            removeEmployee(id);
            return {
                success: true,
                status: status
            };
        } catch (error) {
            const _error = error as AxiosError<ApiErrorResponse>;
            return {
                success: false,
                status: _error.response?.status,
                errorResponse: _error.response?.data
            };
        }
    }

    return {
        employees,
        totalElements,
        totalPages,
        pageSize,
        pageElements,
        pageNumber,
        dispatchGetEmployees,
        dispatchGetEmployee,
        dispatchCreateEmployee,
        dispatchUpdateEmployee,
        dispatchDeleteEmployee
    };
});
