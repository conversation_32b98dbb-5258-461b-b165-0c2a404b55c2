import AppLayout from '@/layout/AppLayout.vue';
import { createRouter, createWebHistory } from 'vue-router';
import { authGuard } from '@/guards/authGuard';
import { subscriptionStatusGuard } from '@/guards/subscriptionGuard';

declare module 'vue-router' {
    // Custom interface for routes meta data object
    interface RouteMeta {
        requiresAuth: boolean;
        requiresGuest?: boolean;
        requiredPrivileges?: string[];
        doesNotRequireActiveSubscription?: boolean;
    }
}

const router = createRouter({
    history: createWebHistory(),
    routes: [
        {
            path: '/',
            component: AppLayout,
            children: [
                {
                    path: '/',
                    name: 'dashboard',
                    component: () => import('@/views/Dashboard.vue'),
                    meta: {
                        requiresAuth: true
                    }
                },
                {
                    path: '/employees',
                    name: 'employees',
                    component: () => import('@/views/employees/Employees.vue'),
                    meta: {
                        requiresAuth: true,
                        requiredPrivileges: ['EMPLOYEE_READ']
                    }
                },
                {
                    path: '/customers',
                    name: 'customers',
                    component: () => import('@/views/customers/CustomersList.vue'),
                    meta: {
                        requiresAuth: true,
                        requiredPrivileges: ['CUSTOMER_READ']
                    }
                },
                {
                    path: '/settings',
                    name: 'settings',
                    component: () => import('@/views/settings/GeneralSettings.vue'),
                    meta: {
                        requiresAuth: true,
                        requiredPrivileges: ['TENANT_MANAGEMENT']
                    }
                },
                {
                    path: '/users',
                    name: 'users',
                    component: () => import('@/views/users/Users.vue'),
                    meta: {
                        requiresAuth: true,
                        requiredPrivileges: ['USER_READ']
                    }
                },
                {
                    path: '/subscription',
                    name: 'subscription',
                    component: () => import('@/views/subscription/Summary.vue'),
                    meta: {
                        requiresAuth: true,
                        doesNotRequireActiveSubscription: true
                    }
                }
            ]
        },
        {
            path: '/auth/login',
            name: 'login',
            component: () => import('@/views/auth/Login.vue')
        },
        {
            path: '/auth/register',
            name: 'register',
            component: () => import('@/views/auth/Login.vue')
        }
    ]
});

router.beforeEach((to, from, next) => {
    authGuard(to, from, next);
});

router.beforeEach((to, from, next) => {
    subscriptionStatusGuard(to, from, next);
});

export default router;
